<template>
  <ResizablePanel
    v-model:visible="visible"
    v-model:full-visible="fullVisible"
    v-model:active-tab="tab"
    :tabs="panelTabs"
    :is-share-page="false"
    background-color="#F9FAFC"
    :full-screenable="false"
  >
    <template #content>
      <iframe
        :src="getPDFViewerUrl(pdfURL)"
        frameborder="0"
        class="size-full"
      />
    </template>
  </ResizablePanel>
</template>

<script setup lang="ts">
import { getPDFViewerUrl } from '@herm-studio/tool.js'

const visible = defineModel('visible', { type: Boolean, default: false })
const pdfURL = defineModel('pdf-url', { type: String, default: '' })

const fullVisible = ref(false)

const tab = defineModel<'preview'>('tab', {
  default: 'preview',
})

const panelTabs = [
  { key: 'preview', label: 'Preview', hide: true },
]
</script>
