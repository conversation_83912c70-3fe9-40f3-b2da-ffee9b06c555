<template>
  <Teleport to=".app-view">
    <div
      v-if="!isSharePage && pushChatContent" class="h-100vh duration-500"
    ></div>

    <div
      class="fixed z-100 block h-100vh flex shrink-0 flex-col transform-gpu overflow-hidden border-l-1px border-[var(--artifacts-code-border)] rounded-tl-25px bg-[var(--artifacts-code-bg)] duration-500 !right-0"
      :class="{
        '!fixed !right-0 !top-0px !w-full !h-full !z-[100] !border-0px !rounded-none': visible && fullVisible,
        'full-panel': !isPc,
        'shadow-[0_0_50px_0_#0000000a]': visible,
      }"
      :style="{
        width: visible ? (isPc ? widthWithUnit : '100%') : '0px',
        minWidth: widthWithUnit,
        transform: visible ? 'translateX(0px)' : 'translateX(100%)',
      }"
    >
      <el-collapse-transition>
        <div
          v-if="!(fullVisible && props.hideHeaderOnSpecificFullscreen)"
          class="flex border-b border-[var(--artifacts-code-border)] bg-[var(--artifacts-code-head-bg)] p-20px"
          :class="{
            'pt-[calc(1.5rem+20px)]': isClient,
          }"
        >
          <div class="flex items-center gap-10px">
            <button
              class="drwan_btn"
              :title="t('artifactsCodeHtml.close')"
              @click="toggleVisibility"
            >
              <i class="i-ju-indent text-10px"></i>
            </button>

            <button
              v-if="isPc && props.fullScreenable"
              class="drwan_btn"
              :title="t('artifactsCodeHtml.fullScreen')"
              @click="toggleFullScreen"
            >
              <i v-if="!fullVisible" class="i-ri-fullscreen-line text-16px"></i>
              <i v-else class="i-ri-fullscreen-exit-line text-16px"></i>
            </button>
            <!-- 左侧附加标题操作插槽 -->
            <slot name="header-actions-left"></slot>
          </div>

          <div
            v-if="tabs && tabs.length > 0"
            class="absolute-x-center h-36px flex-y-c select-none border border-[var(--artifacts-code-tabs-border)] rounded-8px bg-[var(--artifacts-code-tabs)] lt-md:mr-20px"
            :style="{ borderWidth: tabs.filter(tab => !tab.hide).length > 0 ? '1px' : '0px' }"
          >
            <div
              class="absolute left-5px top-5px h-[calc(100%-10px)] w-80px rounded-6px bg-#fff transition-all"
              :style="{ left: `${activeTabIndex * 90 + 5}px` }"
            ></div>

            <div
              v-for="tab in tabs.filter(tab => !tab.hide)"
              :key="tab.key"
              class="relative h-full w-90px flex cursor-pointer items-center justify-center gap-10px text-center text-12px text-#727272 font-400 leading-36px"
              :class="{ '!text-#000000': activeTab === tab.key }"
              @click="handleTabClick(tab.key)"
            >
              <div>{{ tab.label }}</div>

              <slot name="tab-indicator" :tab="tab" :is-active="activeTab === tab.key"></slot>
            </div>
          </div>

          <div class="ml-auto">
            <slot name="header-actions-right"></slot>
          </div>
          <i></i>
        </div>
      </el-collapse-transition>

      <div
        class="mac-scrollbar panel-main flex flex-1 flex-col overflow-auto"
        :class="{
          'transition-opacity duration-200': props.delayContentOnFullscreen,
          'opacity-0 pointer-events-none': props.delayContentOnFullscreen && !contentVisible && isTransitioning,
          'opacity-100': !props.delayContentOnFullscreen || contentVisible || !isTransitioning,
        }"
        :style="{ backgroundColor: props.backgroundColor }"
        @mouseenter="e => ((e.target) as HTMLElement).classList.add('show-scrollbar')"
        @mouseleave="e => ((e.target) as HTMLElement).classList.remove('show-scrollbar')"
      >
        <slot name="content" :active-tab="activeTab"></slot>
      </div>
    </div>
  </Teleport>
</template>

<script setup lang="ts">
import { useI18n } from 'vue-i18n'
import { cssBreakPoints } from '@/config/cssBreakPoints'
import { useAppStore } from '@/stores/app'

const props = withDefaults(defineProps<{
  tabs?: Tab[]
  isSharePage?: boolean
  backgroundColor?: string
  fullScreenable?: boolean
  width?: number | string
  pushChatContent?: boolean
  hideHeaderOnSpecificFullscreen?: boolean
  delayContentOnFullscreen?: boolean // 是否在全屏/非全屏切换时延迟内容更新以提升性能
}>(), {
  fullScreenable: true,
  // pushChatContent为true时，会随着侧边栏展开，向左推开聊天内容
  pushChatContent: true,
  hideHeaderOnSpecificFullscreen: false,
  delayContentOnFullscreen: false,
})
const emit = defineEmits<{
  (e: 'tabClick', value: string): void
  (e: 'toggleFullScreen', value: boolean): void
}>()
// 内容显示控制状态
const contentVisible = ref(true)
const isTransitioning = ref(false)

interface Tab {
  key: string
  label: string
  hide?: boolean
}

const visible = defineModel<boolean>('visible')
const fullVisible = defineModel<boolean>('fullVisible')
const activeTab = defineModel<string>('activeTab')
const { t } = useI18n()
const appStore = useAppStore()
const { isPc, isClient, isResizablePanelVisible } = storeToRefs(appStore)

watch(visible, (newValue) => {
  isResizablePanelVisible.value = newValue || false
})

// 使用useBreakpoints进行响应式断点检测
const breakpoints = useBreakpoints(cssBreakPoints)
const larger2xl = breakpoints.greater('2xl') // > 1536px
const largerXl = breakpoints.greater('xl') // > 1280px
const largerLg = breakpoints.greater('lg') // > 1024px
const largerMd = breakpoints.greaterOrEqual('md') // >= 768px

const widthWithUnit = computed(() => {
  if (!props.width) {
    switch (true) {
      case larger2xl.value: return '800px'
      case largerXl.value: return '700px'
      case largerLg.value: return '600px'
      case largerMd.value: return '500px'
      case !isPc.value: return '100%'
      default: return '30vw'
    }
  }

  // 如果是纯数字，添加px单位
  if (typeof props.width === 'number' || /^\d+$/.test(props.width.toString())) {
    return `${props.width}px`
  }

  // 不是纯数字，直接返回（比如70%、25vw等）
  return props.width
})

const activeTabIndex = computed(() => {
  if (!props.tabs) { return 0 }
  const index = props.tabs.findIndex(tab => tab.key === activeTab.value)
  return index >= 0 ? index : 0
})

const toggleVisibility = () => {
  if (fullVisible.value) {
    // 从全屏退出时应用优化
    if (props.delayContentOnFullscreen) {
      isTransitioning.value = true
      contentVisible.value = false
    }

    fullVisible.value = false
    emit('toggleFullScreen', false)

    // 等待过渡完成后显示内容
    if (props.delayContentOnFullscreen) {
      nextTick(() => {
        setTimeout(() => {
          contentVisible.value = true
          isTransitioning.value = false
        }, 500)
      })
    }
  }
  else {
    if (visible.value) {
      // 关闭侧边栏 - 直接关闭，不应用优化
      visible.value = false
    }
    else {
      // 显示侧边栏 - 应用优化
      visible.value = true
      if (props.delayContentOnFullscreen) {
        // 显示时先隐藏内容，等动画完成后再显示
        isTransitioning.value = true
        contentVisible.value = false

        nextTick(() => {
          setTimeout(() => {
            contentVisible.value = true
            isTransitioning.value = false
          }, 500)
        })
      }
    }
  }
}

const toggleFullScreen = () => {
  // 切换全屏状态
  const newFullVisible = !fullVisible.value
  fullVisible.value = newFullVisible

  // 触发事件
  emit('toggleFullScreen', newFullVisible)

  // 只有在启用延迟内容更新时才执行延迟逻辑
  if (props.delayContentOnFullscreen) {
    // 开始过渡时隐藏内容
    isTransitioning.value = true
    contentVisible.value = false

    // 等待过渡动画完成后再显示内容
    nextTick(() => {
      setTimeout(() => {
        contentVisible.value = true
        isTransitioning.value = false
      }, 500)
    })
  }
}

const handleTabClick = (tabKey: string) => {
  activeTab.value = tabKey
  emit('tabClick', tabKey)
}
</script>

<style scoped>
.full-panel {
  position: fixed;
  width: 100%;
  z-index: 99;
}

.drwan_btn {
  @apply size-36px flex-c cursor-pointer border border-[var(--artifacts-code-border)] rounded-8px bg-[var(--artifacts-code-bg)] text-#727272;
}

.panel-main {
  scrollbar-gutter: auto !important;
}
</style>
